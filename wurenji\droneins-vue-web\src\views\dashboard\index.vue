<template>
  <div class="dashboard">
    <!-- 科技背景网格 -->
    <div class="tech-grid"></div>
    <div class="tech-particles"></div>

    <!-- 退出登录按钮 -->
    <div class="logout-container">
      <button @click="handleLogout" class="logout-btn">
        <i class="fas fa-sign-out-alt"></i>
        <span>退出登录</span>
      </button>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 标题区域 -->
      <div class="header-section">
        <h1 class="main-title">无人机智能管控系统</h1>
        <div class="title-line"></div>
      </div>

      <!-- 卡片滑动区域 -->
      <div class="cards-container">
        <div class="cards-wrapper" ref="cardsWrapper">
          <div
            v-for="(module, index) in modules"
            :key="module.id"
            class="module-card"
            :class="{ 'active': index === activeIndex }"
            @click="selectCard(index)"
          >
            <div class="card-border"></div>
            <div class="card-content">
              <div class="card-icon">
                <div class="icon-container">
                  <i :class="module.icon"></i>
                </div>
                <div class="icon-glow"></div>
              </div>
              <div class="card-text">
                <h3 class="card-title">{{ module.title }}</h3>
                <div class="card-description">{{ module.description }}</div>
              </div>
              <div class="card-button" v-if="index === activeIndex">
                <button class="enter-btn" @click.stop="enterModule(module)">
                  <span>进入</span>
                  <i class="fas fa-arrow-right"></i>
                </button>
              </div>
            </div>
            <div class="card-reflection"></div>
          </div>
        </div>
      </div>

      <!-- 底部指示器 -->
      <div class="bottom-indicators">
        <div
          v-for="(module, index) in modules"
          :key="module.id"
          class="indicator"
          :class="{ 'active': index === activeIndex }"
          @click="selectCard(index)"
        >
          <div class="indicator-dot"></div>
          <span class="indicator-label">{{ module.title }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

const router = useRouter()

// 当前激活的卡片索引
const activeIndex = ref(3) // 默认选中中间的卡片

// 卡片容器引用
const cardsWrapper = ref<HTMLElement>()

// 模块数据
const modules = ref([
  {
    id: 1,
    title: '无人机管理',
    description: '设备状态监控与管理',
    icon: 'fas fa-helicopter'
  },
  {
    id: 2,
    title: '任务规划',
    description: '智能路径规划系统',
    icon: 'fas fa-route'
  },
  {
    id: 3,
    title: '数据采集',
    description: '多维度数据收集',
    icon: 'fas fa-camera'
  },
  {
    id: 4,
    title: '智能分析',
    description: 'AI驱动数据分析',
    icon: 'fas fa-brain'
  },
  {
    id: 5,
    title: '数据报告',
    description: '可视化报表生成',
    icon: 'fas fa-chart-line'
  },
  {
    id: 6,
    title: '系统设置',
    description: '系统参数配置',
    icon: 'fas fa-cog'
  },
  {
    id: 7,
    title: '用户管理',
    description: '权限与用户控制',
    icon: 'fas fa-users'
  },
  {
    id: 8,
    title: '监控中心',
    description: '实时状态监控',
    icon: 'fas fa-desktop'
  }
])

// 进入模块
function enterModule(module: any) {
  ElMessage.success(`正在进入${module.title}模块...`)
  // 这里可以添加路由跳转逻辑
  // router.push(`/${module.route}`)
}

// 选择卡片
function selectCard(index: number) {
  activeIndex.value = index
  updateCardsPosition()
}

// 更新卡片位置
function updateCardsPosition() {
  if (!cardsWrapper.value) return

  const cards = cardsWrapper.value.children
  const totalCards = cards.length
  const centerIndex = activeIndex.value
  const cardSpacing = 320 // 卡片间距

  for (let i = 0; i < totalCards; i++) {
    const card = cards[i] as HTMLElement
    const offset = i - centerIndex
    const absOffset = Math.abs(offset)

    // 优化的卡片间距和位置计算 - 确保左右对称显示
    const translateX = offset * cardSpacing
    const translateY = absOffset * 10 // 轻微的垂直偏移
    const translateZ = -absOffset * 50 // 适度的3D深度
    const rotateY = offset * 4 // 轻微的旋转角度

    // 缩放效果 - 中心卡片正常大小，其他卡片略小
    const scale = i === centerIndex ? 1.0 : Math.max(0.9, 1 - absOffset * 0.05)

    // 层级控制 - 确保中心卡片在最前面
    let zIndex = 20 - absOffset
    if (i === centerIndex) zIndex = 30

    // 透明度控制 - 显示中心+左右各2个卡片
    let opacity = 1
    if (absOffset > 2) {
      opacity = 0.3 // 超出显示范围的卡片半透明
    } else if (absOffset > 0) {
      opacity = 0.8 // 侧边卡片稍微透明
    }

    // 应用变换
    card.style.transform = `
      translateX(${translateX}px)
      translateY(${translateY}px)
      translateZ(${translateZ}px)
      rotateY(${rotateY}deg)
      scale(${scale})
    `
    card.style.zIndex = zIndex.toString()
    card.style.opacity = opacity.toString()

    // 模糊效果 - 只对距离较远的卡片应用
    const blurAmount = absOffset > 2 ? Math.min((absOffset - 2) * 2, 6) : 0
    card.style.filter = `blur(${blurAmount}px)`
  }

  // 智能容器偏移计算 - 避免边界空白
  const visibleCards = 5 // 显示中心+左右各2个卡片
  let containerOffset = 0

  // 智能边界控制 - 确保始终显示5个卡片，无空白
  if (totalCards <= visibleCards) {
    // 如果卡片总数少于等于5个，直接居中显示所有卡片
    containerOffset = -((totalCards - 1) / 2) * cardSpacing
  } else {
    // 对于8个卡片的情况，精确控制显示范围
    // 显示规则：始终显示5个连续的卡片
    // 索引0,1,2 -> 显示卡片0,1,2,3,4 (中心在索引2)
    // 索引3,4 -> 显示卡片1,2,3,4,5 (中心在索引3) 或 2,3,4,5,6 (中心在索引4)
    // 索引5,6,7 -> 显示卡片3,4,5,6,7 (中心在索引5)

    let displayCenterIndex = centerIndex

    if (centerIndex <= 2) {
      // 选择前3个卡片时，固定显示前5个卡片，中心在索引2
      displayCenterIndex = 2
    } else if (centerIndex >= totalCards - 3) {
      // 选择后3个卡片时，固定显示后5个卡片，中心在索引5
      displayCenterIndex = totalCards - 3
    }
    // 中间的卡片(索引3,4)正常居中显示

    containerOffset = -displayCenterIndex * cardSpacing
  }

  cardsWrapper.value.style.transform = `translateX(${containerOffset}px)`
}

// 退出登录
function handleLogout() {
  sessionStorage.removeItem('isLoggedIn')
  sessionStorage.removeItem('userInfo')
  ElMessage.success('已安全退出系统')
  router.push('/login')
}

onMounted(() => {
  updateCardsPosition()
})
</script>

<style scoped>
.dashboard {
  width: 100vw;
  height: 100vh;
  background:
    radial-gradient(ellipse at 20% 50%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(ellipse at 80% 20%, rgba(0, 150, 255, 0.1) 0%, transparent 50%),
    radial-gradient(ellipse at 40% 80%, rgba(0, 200, 255, 0.08) 0%, transparent 50%),
    linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
  color: white;
  padding: 0;
  margin: 0;
  position: fixed;
  top: 0;
  left: 0;
  overflow: hidden;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* 科技网格背景 */
.tech-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: gridMove 20s linear infinite;
  opacity: 0.3;
  z-index: 1;
}

@keyframes gridMove {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}

/* 科技粒子效果 */
.tech-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(2px 2px at 20px 30px, #00ffff, transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(0, 255, 255, 0.4), transparent),
    radial-gradient(1px 1px at 90px 40px, #00d4ff, transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(0, 212, 255, 0.3), transparent),
    radial-gradient(2px 2px at 160px 30px, #00ffff, transparent);
  background-repeat: repeat;
  background-size: 200px 100px;
  animation: particleFloat 25s linear infinite;
  opacity: 0.4;
  z-index: 1;
}

@keyframes particleFloat {
  0% { transform: translateY(0px) rotate(0deg); }
  100% { transform: translateY(-100px) rotate(360deg); }
}

/* 退出登录按钮 */
.logout-container {
  position: absolute;
  top: 30px;
  right: 30px;
  z-index: 100;
}

.logout-btn {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(0, 255, 255, 0.3);
  color: #00ffff;
  padding: 12px 24px;
  border-radius: 30px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.4s ease;
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  gap: 8px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.logout-btn:hover {
  background: rgba(0, 255, 255, 0.1);
  border-color: rgba(0, 255, 255, 0.6);
  box-shadow:
    0 0 30px rgba(0, 255, 255, 0.3),
    inset 0 0 20px rgba(0, 255, 255, 0.1);
  transform: translateY(-2px);
}

.logout-btn i {
  font-size: 16px;
}

/* 主要内容区域 */
.main-content {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  padding: 2vh 2vw;
  box-sizing: border-box;
}

/* 标题区域 */
.header-section {
  text-align: center;
  margin-bottom: 60px;
  z-index: 3;
}

.main-title {
  font-size: 3.5rem;
  font-weight: 300;
  color: #ffffff;
  margin: 0;
  text-shadow:
    0 0 20px rgba(0, 255, 255, 0.5),
    0 0 40px rgba(0, 255, 255, 0.3);
  letter-spacing: 3px;
  background: linear-gradient(135deg, #ffffff 0%, #00ffff 50%, #ffffff 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: titleGlow 3s ease-in-out infinite alternate;
}

@keyframes titleGlow {
  0% {
    text-shadow:
      0 0 20px rgba(0, 255, 255, 0.5),
      0 0 40px rgba(0, 255, 255, 0.3);
  }
  100% {
    text-shadow:
      0 0 30px rgba(0, 255, 255, 0.8),
      0 0 60px rgba(0, 255, 255, 0.5);
  }
}

.title-line {
  width: 200px;
  height: 2px;
  background: linear-gradient(90deg, transparent, #00ffff, transparent);
  margin: 20px auto;
  border-radius: 1px;
  box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

/* 卡片容器 */
.cards-container {
  width: 100%;
  height: 65vh;
  max-height: 600px;
  min-height: 520px;
  display: flex;
  align-items: center;
  justify-content: center;
  perspective: 2000px;
  perspective-origin: center center;
  margin-bottom: 50px;
  overflow: hidden; /* 隐藏超出部分，避免显示多余卡片 */
}

.cards-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  transform-style: preserve-3d;
  transition: transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  min-width: 2560px; /* 确保容器足够宽：8张卡片 × 320px = 2560px */
}

/* 模块卡片 */
.module-card {
  position: absolute;
  width: 300px;
  height: 480px;
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  backdrop-filter: blur(20px);
  transform-style: preserve-3d;
  overflow: hidden;
  position: relative;
}

/* 卡片边框发光效果 */
.card-border {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 20px;
  padding: 1px;
  background: linear-gradient(135deg,
    rgba(0, 255, 255, 0.3) 0%,
    rgba(0, 255, 255, 0.1) 25%,
    transparent 50%,
    rgba(0, 255, 255, 0.1) 75%,
    rgba(0, 255, 255, 0.3) 100%
  );
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  opacity: 0;
  transition: opacity 0.6s ease;
}

.module-card.active .card-border {
  opacity: 1;
  animation: borderGlow 2s ease-in-out infinite alternate;
}

@keyframes borderGlow {
  0% {
    background: linear-gradient(135deg,
      rgba(0, 255, 255, 0.3) 0%,
      rgba(0, 255, 255, 0.1) 25%,
      transparent 50%,
      rgba(0, 255, 255, 0.1) 75%,
      rgba(0, 255, 255, 0.3) 100%
    );
  }
  100% {
    background: linear-gradient(135deg,
      rgba(0, 255, 255, 0.6) 0%,
      rgba(0, 255, 255, 0.3) 25%,
      rgba(0, 255, 255, 0.1) 50%,
      rgba(0, 255, 255, 0.3) 75%,
      rgba(0, 255, 255, 0.6) 100%
    );
  }
}

.module-card.active {
  background: rgba(0, 0, 0, 0.6);
  border-color: rgba(0, 255, 255, 0.6);
  box-shadow:
    0 20px 60px rgba(0, 255, 255, 0.3),
    0 0 40px rgba(0, 255, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transform: translateZ(30px) scale(1.05) !important;
}

.module-card:not(.active) {
  background: rgba(0, 0, 0, 0.2);
  border-color: rgba(0, 255, 255, 0.1);
}

/* 卡片反射效果 */
.card-reflection {
  position: absolute;
  bottom: -100%;
  left: 0;
  right: 0;
  height: 100%;
  background: linear-gradient(to bottom,
    rgba(0, 255, 255, 0.1) 0%,
    transparent 50%
  );
  transform: scaleY(-1);
  opacity: 0.3;
  pointer-events: none;
}

.card-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 35px 25px 30px 25px;
  text-align: center;
  position: relative;
  z-index: 2;
  box-sizing: border-box;
}

/* 卡片图标 */
.card-icon {
  position: relative;
  flex-shrink: 0;
  margin-bottom: 10px;
}

.icon-container {
  width: 100px;
  height: 100px;
  border: 2px solid rgba(0, 255, 255, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 255, 255, 0.1);
  transition: all 0.4s ease;
  position: relative;
  z-index: 2;
}

.icon-container i {
  font-size: 40px;
  color: #00ffff;
  transition: all 0.4s ease;
}

.icon-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(0, 255, 255, 0.3) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: all 0.4s ease;
  animation: iconPulse 2s ease-in-out infinite;
}

@keyframes iconPulse {
  0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.3; }
  50% { transform: translate(-50%, -50%) scale(1.2); opacity: 0.6; }
}

.module-card.active .icon-container {
  border-color: rgba(0, 255, 255, 0.8);
  background: rgba(0, 255, 255, 0.2);
  box-shadow: 0 0 30px rgba(0, 255, 255, 0.5);
}

.module-card.active .icon-container i {
  color: #ffffff;
  text-shadow: 0 0 20px rgba(0, 255, 255, 1);
}

.module-card.active .icon-glow {
  opacity: 0.8;
}

/* 卡片文本区域 */
.card-text {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 20px 0;
}

/* 卡片标题 */
.card-title {
  font-size: 24px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  margin: 0 0 15px 0;
  transition: all 0.4s ease;
  letter-spacing: 1px;
  line-height: 1.2;
}

.card-description {
  font-size: 15px;
  color: rgba(255, 255, 255, 0.6);
  margin: 0;
  line-height: 1.6;
  transition: all 0.4s ease;
  max-width: 100%;
}

.module-card.active .card-title {
  color: #00ffff;
  text-shadow: 0 0 20px rgba(0, 255, 255, 0.8);
}

.module-card.active .card-description {
  color: rgba(255, 255, 255, 0.8);
}

/* 进入按钮 */
.card-button {
  width: 100%;
  flex-shrink: 0;
  padding: 0;
  margin: 0;
}

.enter-btn {
  width: 100%;
  background: rgba(0, 255, 255, 0.15);
  border: 2px solid rgba(0, 255, 255, 0.4);
  color: #00ffff;
  padding: 18px 24px;
  border-radius: 35px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.4s ease;
  backdrop-filter: blur(15px);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  text-transform: uppercase;
  letter-spacing: 1.5px;
  box-shadow: 0 4px 15px rgba(0, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.enter-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.6s ease;
}

.enter-btn:hover {
  background: rgba(0, 255, 255, 0.25);
  border-color: rgba(0, 255, 255, 0.7);
  box-shadow:
    0 6px 25px rgba(0, 255, 255, 0.4),
    0 0 40px rgba(0, 255, 255, 0.3);
  transform: translateY(-3px);
}

.enter-btn:hover::before {
  left: 100%;
}

.enter-btn i {
  font-size: 14px;
  transition: transform 0.3s ease;
}

.enter-btn:hover i {
  transform: translateX(4px);
}

/* 底部指示器 */
.bottom-indicators {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
  flex-wrap: nowrap; /* 强制不换行 */
  width: 100%;
  margin-top: 60px;
  padding: 20px 10px;
  overflow-x: auto; /* 如果内容过宽，允许水平滚动 */
  overflow-y: hidden;
}

/* 隐藏滚动条但保持滚动功能 */
.bottom-indicators::-webkit-scrollbar {
  display: none;
}

.bottom-indicators {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.indicator {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  transition: all 0.4s ease;
  padding: 10px 16px;
  border-radius: 25px;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(0, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  min-width: 120px;
  max-width: 140px;
  justify-content: center;
  flex-shrink: 0; /* 防止收缩 */
}

.indicator:hover {
  background: rgba(0, 255, 255, 0.1);
  border-color: rgba(0, 255, 255, 0.4);
  transform: translateY(-2px);
}

.indicator-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(0, 255, 255, 0.4);
  transition: all 0.4s ease;
  position: relative;
  flex-shrink: 0;
}

.indicator-dot::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: rgba(0, 255, 255, 0.1);
  transform: translate(-50%, -50%);
  transition: all 0.4s ease;
  opacity: 0;
}

.indicator-label {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.7);
  transition: all 0.4s ease;
  text-align: center;
  letter-spacing: 0.5px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.indicator.active {
  background: rgba(0, 255, 255, 0.15);
  border-color: rgba(0, 255, 255, 0.6);
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
}

.indicator.active .indicator-dot {
  background: #00ffff;
  box-shadow: 0 0 15px rgba(0, 255, 255, 0.8);
  transform: scale(1.2);
}

.indicator.active .indicator-dot::before {
  opacity: 1;
  background: rgba(0, 255, 255, 0.3);
  width: 24px;
  height: 24px;
  animation: dotPulse 2s ease-in-out infinite;
}

.indicator.active .indicator-label {
  color: #00ffff;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

@keyframes dotPulse {
  0%, 100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.3;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0.6;
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-title {
    font-size: 3rem;
  }

  .cards-container {
    height: 60vh;
    max-height: 550px;
    min-height: 480px;
  }

  .module-card {
    width: 280px;
    height: 440px;
  }

  .card-content {
    padding: 30px 20px 25px 20px;
  }

  .bottom-indicators {
    gap: 12px;
    padding: 20px 5px;
  }

  .indicator {
    min-width: 110px;
    max-width: 120px;
    padding: 8px 14px;
  }

  .indicator-label {
    font-size: 12px;
  }
}

@media (max-width: 768px) {
  .main-title {
    font-size: 2.5rem;
    letter-spacing: 2px;
  }

  .cards-container {
    height: 55vh;
    max-height: 500px;
    min-height: 420px;
    margin-bottom: 40px;
  }

  .module-card {
    width: 260px;
    height: 400px;
  }

  .card-content {
    padding: 25px 18px 22px 18px;
  }

  .card-text {
    padding: 15px 0;
  }

  .icon-container {
    width: 80px;
    height: 80px;
  }

  .icon-container i {
    font-size: 32px;
  }

  .card-title {
    font-size: 20px;
    margin: 0 0 10px 0;
  }

  .card-description {
    font-size: 13px;
  }

  .enter-btn {
    padding: 16px 20px;
    font-size: 15px;
  }

  .bottom-indicators {
    gap: 10px;
    margin-top: 40px;
    padding: 15px 5px;
  }

  .indicator {
    min-width: 100px;
    max-width: 110px;
    padding: 8px 12px;
  }

  .indicator-label {
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .main-title {
    font-size: 2rem;
    letter-spacing: 1px;
  }

  .header-section {
    margin-bottom: 25px;
  }

  .cards-container {
    height: 50vh;
    max-height: 450px;
    min-height: 380px;
    margin-bottom: 35px;
  }

  .module-card {
    width: 240px;
    height: 360px;
  }

  .card-content {
    padding: 20px 15px 18px 15px;
  }

  .card-text {
    padding: 12px 0;
  }

  .icon-container {
    width: 70px;
    height: 70px;
  }

  .icon-container i {
    font-size: 28px;
  }

  .card-title {
    font-size: 18px;
    margin: 0 0 8px 0;
  }

  .card-description {
    font-size: 12px;
  }

  .enter-btn {
    padding: 14px 18px;
    font-size: 14px;
  }

  .bottom-indicators {
    gap: 8px;
    margin-top: 30px;
    padding: 15px 5px;
  }

  .indicator {
    min-width: 90px;
    max-width: 100px;
    padding: 6px 10px;
  }

  .indicator-dot {
    width: 10px;
    height: 10px;
  }

  .indicator-label {
    font-size: 10px;
  }

  .logout-container {
    top: 20px;
    right: 20px;
  }

  .logout-btn {
    padding: 10px 16px;
    font-size: 12px;
  }
}


</style>
