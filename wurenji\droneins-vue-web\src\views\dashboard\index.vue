<template>
  <div class="dashboard">
    <!-- 科技背景网格 -->
    <div class="tech-grid"></div>
    <div class="tech-particles"></div>

    <!-- 退出登录按钮 -->
    <div class="logout-container">
      <button @click="handleLogout" class="logout-btn">
        <i class="fas fa-sign-out-alt"></i>
        <span>退出登录</span>
      </button>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 标题区域 -->
      <div class="header-section">
        <h1 class="main-title">无人机智能管控系统</h1>
        <div class="title-line"></div>
      </div>

      <!-- 卡片滑动区域 -->
      <div class="cards-container" @touchstart="handleTouchStart" @touchend="handleTouchEnd">
        <div class="cards-wrapper" ref="cardsWrapper">
          <div
            v-for="(module, index) in modules"
            :key="module.id"
            class="module-card"
            :class="{ 'active': index === activeIndex }"
            @click="selectCard(index)"
          >
            <div class="card-border"></div>
            <div class="card-content">
              <div class="card-icon">
                <div class="icon-container">
                  <i :class="module.icon"></i>
                </div>
                <div class="icon-glow"></div>
              </div>
              <div class="card-text">
                <h3 class="card-title">{{ module.title }}</h3>
                <div class="card-description">{{ module.description }}</div>
              </div>
              <div class="card-button" v-if="index === activeIndex">
                <button class="enter-btn" @click.stop="enterModule(module)">
                  <span>进入</span>
                  <i class="fas fa-arrow-right"></i>
                </button>
              </div>
            </div>
            <div class="card-reflection"></div>
          </div>
        </div>
      </div>

      <!-- 底部指示器 -->
      <div class="bottom-indicators">
        <div
          v-for="(module, index) in modules"
          :key="module.id"
          class="indicator"
          :class="{ 'active': index === activeIndex }"
          @click="selectCard(index)"
        >
          <div class="indicator-dot"></div>
          <span class="indicator-label">{{ module.title }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

const router = useRouter()

// 当前激活的卡片索引
const activeIndex = ref(3) // 默认选中中间的卡片

// 卡片容器引用
const cardsWrapper = ref<HTMLElement>()

// 模块数据
const modules = ref([
  {
    id: 1,
    title: '无人机管理',
    description: '设备状态监控与管理',
    icon: 'fas fa-helicopter'
  },
  {
    id: 2,
    title: '任务规划',
    description: '智能路径规划系统',
    icon: 'fas fa-route'
  },
  {
    id: 3,
    title: '数据采集',
    description: '多维度数据收集',
    icon: 'fas fa-camera'
  },
  {
    id: 4,
    title: '智能分析',
    description: 'AI驱动数据分析',
    icon: 'fas fa-brain'
  },
  {
    id: 5,
    title: '数据报告',
    description: '可视化报表生成',
    icon: 'fas fa-chart-line'
  },
  {
    id: 6,
    title: '系统设置',
    description: '系统参数配置',
    icon: 'fas fa-cog'
  },
  {
    id: 7,
    title: '用户管理',
    description: '权限与用户控制',
    icon: 'fas fa-users'
  },
  {
    id: 8,
    title: '监控中心',
    description: '实时状态监控',
    icon: 'fas fa-desktop'
  }
])

// 进入模块
function enterModule(module: any) {
  ElMessage.success(`正在进入${module.title}模块...`)
  // 这里可以添加路由跳转逻辑
  // router.push(`/${module.route}`)
}

// 防抖变量
let isAnimating = false
let animationTimeout: number | null = null

// 选择卡片 - 添加防抖机制
function selectCard(index: number) {
  if (index >= 0 && index < modules.value.length && !isAnimating) {
    isAnimating = true
    activeIndex.value = index
    updateCardsPosition()

    // 清除之前的超时
    if (animationTimeout) {
      clearTimeout(animationTimeout)
    }

    // 设置动画完成标志
    animationTimeout = setTimeout(() => {
      isAnimating = false
    }, 1500) // 与最长的动画时间保持一致（包括延迟）
  }
}

// 鼠标滚轮支持
function handleWheel(event: Event) {
  const wheelEvent = event as WheelEvent
  wheelEvent.preventDefault()
  const delta = wheelEvent.deltaY > 0 ? 1 : -1
  const newIndex = activeIndex.value + delta
  selectCard(newIndex)
}

// 键盘导航支持
function handleKeydown(event: Event) {
  const keyEvent = event as KeyboardEvent
  switch (keyEvent.key) {
    case 'ArrowLeft':
      keyEvent.preventDefault()
      selectCard(activeIndex.value - 1)
      break
    case 'ArrowRight':
      keyEvent.preventDefault()
      selectCard(activeIndex.value + 1)
      break
    case 'Enter':
    case ' ':
      keyEvent.preventDefault()
      enterModule(modules.value[activeIndex.value])
      break
  }
}

// 触摸滑动支持
let touchStartX = 0

function handleTouchStart(event: TouchEvent) {
  touchStartX = event.touches[0].clientX
}

function handleTouchEnd(event: TouchEvent) {
  const touchEndX = event.changedTouches[0].clientX
  const deltaX = touchStartX - touchEndX
  const minSwipeDistance = 50 // 最小滑动距离

  if (Math.abs(deltaX) > minSwipeDistance) {
    if (deltaX > 0) {
      // 向左滑动，显示下一个卡片
      selectCard(activeIndex.value + 1)
    } else {
      // 向右滑动，显示上一个卡片
      selectCard(activeIndex.value - 1)
    }
  }
}

// 更新卡片位置 - 完美的水平展开布局，所有卡片可见，向两边逐渐透明
function updateCardsPosition() {
  if (!cardsWrapper.value) return

  const cards = cardsWrapper.value.children
  const totalCards = cards.length
  const centerIndex = activeIndex.value
  const cardSpacing = 300 // 优化卡片间距，确保所有卡片都能展示

  // 使用requestAnimationFrame确保动画流畅
  requestAnimationFrame(() => {
    for (let i = 0; i < totalCards; i++) {
      const card = cards[i] as HTMLElement
      const offset = i - centerIndex
      const absOffset = Math.abs(offset)

      // 水平位置：以中心卡片为基准，左右完美展开
      const translateX = offset * cardSpacing

      // 垂直位置：中心卡片稍微上移，营造焦点效果
      let translateY = 0
      if (i === centerIndex) {
        translateY = -25 // 中心卡片上移
      } else {
        translateY = absOffset * 5 // 远离中心的卡片稍微下移，增加层次感
      }

      // 3D深度效果：创造立体层次感
      const translateZ = i === centerIndex ? 80 : -absOffset * 25

      // 旋转效果：轻微的Y轴旋转，增加透视感
      const rotateY = offset * 2

      // 缩放效果：从中心向两边平滑递减
      let scale = 1.0
      if (i === centerIndex) {
        scale = 1.15 // 中心卡片适度放大
      } else {
        // 使用平滑的缩放函数，确保所有卡片都有合适的大小
        scale = Math.max(0.65, 1 - absOffset * 0.12)
      }

      // 层级控制：确保中心卡片在最前面
      let zIndex = 100 - absOffset
      if (i === centerIndex) zIndex = 200

      // 透明度控制：关键优化 - 确保所有卡片可见，向两边逐渐透明
      let opacity = 1.0
      if (i === centerIndex) {
        opacity = 1.0 // 中心卡片完全不透明
      } else {
        // 使用更平滑的透明度衰减曲线
        // 确保最远的卡片也有足够的可见度
        const maxVisibleDistance = (totalCards - 1) / 2
        const normalizedDistance = absOffset / maxVisibleDistance

        // 使用二次函数创造更自然的透明度过渡
        opacity = Math.max(0.25, 1 - Math.pow(normalizedDistance, 1.5) * 0.75)
      }

      // 动画延迟：从中心向外波浪式展开
      const animationDelay = absOffset * 50

      setTimeout(() => {
        // 应用所有变换
        card.style.transform = `
          translateX(${translateX}px)
          translateY(${translateY}px)
          translateZ(${translateZ}px)
          rotateY(${rotateY}deg)
          scale(${scale})
        `
        card.style.zIndex = zIndex.toString()
        card.style.opacity = opacity.toString()

        // 模糊效果：只对最远的卡片应用轻微模糊
        const blurAmount = absOffset > 3 ? (absOffset - 3) * 1 : 0
        card.style.filter = `blur(${blurAmount}px)`

        // 阴影效果：根据位置和透明度调整
        if (i === centerIndex) {
          card.style.boxShadow = `
            0 35px 100px rgba(0, 255, 255, 0.7),
            0 0 70px rgba(0, 255, 255, 0.5),
            0 0 120px rgba(0, 255, 255, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.2)
          `
        } else {
          const shadowIntensity = opacity * 0.5
          card.style.boxShadow = `
            0 ${10 + absOffset * 5}px ${20 + absOffset * 10}px rgba(0, 0, 0, 0.4),
            0 0 ${15 + absOffset * 5}px rgba(0, 255, 255, ${shadowIntensity})
          `
        }
      }, animationDelay)
    }

    // 容器保持居中，不需要偏移
    if (cardsWrapper.value) {
      cardsWrapper.value.style.transform = 'translateX(0px)'
    }
  })
}

// 退出登录
function handleLogout() {
  sessionStorage.removeItem('isLoggedIn')
  sessionStorage.removeItem('userInfo')
  ElMessage.success('已安全退出系统')
  router.push('/login')
}

onMounted(() => {
  // 延迟初始化，让页面先渲染
  setTimeout(() => {
    updateCardsPosition()
  }, 100)

  // 添加事件监听器
  const cardsContainer = document.querySelector('.cards-container')
  if (cardsContainer) {
    cardsContainer.addEventListener('wheel', handleWheel, { passive: false })
  }

  // 添加键盘事件监听器
  document.addEventListener('keydown', handleKeydown)
})

// 清理事件监听器
onUnmounted(() => {
  const cardsContainer = document.querySelector('.cards-container')
  if (cardsContainer) {
    cardsContainer.removeEventListener('wheel', handleWheel)
  }
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
.dashboard {
  width: 100vw;
  height: 100vh;
  background:
    radial-gradient(ellipse at 20% 50%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(ellipse at 80% 20%, rgba(0, 150, 255, 0.1) 0%, transparent 50%),
    radial-gradient(ellipse at 40% 80%, rgba(0, 200, 255, 0.08) 0%, transparent 50%),
    linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
  color: white;
  padding: 0;
  margin: 0;
  position: fixed;
  top: 0;
  left: 0;
  overflow: hidden;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* 科技网格背景 */
.tech-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: gridMove 20s linear infinite;
  opacity: 0.3;
  z-index: 1;
}

@keyframes gridMove {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}

/* 科技粒子效果 */
.tech-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(2px 2px at 20px 30px, #00ffff, transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(0, 255, 255, 0.4), transparent),
    radial-gradient(1px 1px at 90px 40px, #00d4ff, transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(0, 212, 255, 0.3), transparent),
    radial-gradient(2px 2px at 160px 30px, #00ffff, transparent);
  background-repeat: repeat;
  background-size: 200px 100px;
  animation: particleFloat 25s linear infinite;
  opacity: 0.4;
  z-index: 1;
}

@keyframes particleFloat {
  0% { transform: translateY(0px) rotate(0deg); }
  100% { transform: translateY(-100px) rotate(360deg); }
}

/* 退出登录按钮 */
.logout-container {
  position: absolute;
  top: 30px;
  right: 30px;
  z-index: 100;
}

.logout-btn {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(0, 255, 255, 0.3);
  color: #00ffff;
  padding: 12px 24px;
  border-radius: 30px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.4s ease;
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  gap: 8px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.logout-btn:hover {
  background: rgba(0, 255, 255, 0.1);
  border-color: rgba(0, 255, 255, 0.6);
  box-shadow:
    0 0 30px rgba(0, 255, 255, 0.3),
    inset 0 0 20px rgba(0, 255, 255, 0.1);
  transform: translateY(-2px);
}

.logout-btn i {
  font-size: 16px;
}

/* 主要内容区域 */
.main-content {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  padding: 2vh 2vw;
  box-sizing: border-box;
}

/* 标题区域 */
.header-section {
  text-align: center;
  margin-bottom: 60px;
  z-index: 3;
}

.main-title {
  font-size: 3.5rem;
  font-weight: 300;
  color: #ffffff;
  margin: 0;
  text-shadow:
    0 0 20px rgba(0, 255, 255, 0.5),
    0 0 40px rgba(0, 255, 255, 0.3);
  letter-spacing: 3px;
  background: linear-gradient(135deg, #ffffff 0%, #00ffff 50%, #ffffff 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: titleGlow 3s ease-in-out infinite alternate;
}

@keyframes titleGlow {
  0% {
    text-shadow:
      0 0 20px rgba(0, 255, 255, 0.5),
      0 0 40px rgba(0, 255, 255, 0.3);
  }
  100% {
    text-shadow:
      0 0 30px rgba(0, 255, 255, 0.8),
      0 0 60px rgba(0, 255, 255, 0.5);
  }
}

.title-line {
  width: 200px;
  height: 2px;
  background: linear-gradient(90deg, transparent, #00ffff, transparent);
  margin: 20px auto;
  border-radius: 1px;
  box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

/* 卡片容器 - 完美的水平展开布局，展示所有卡片 */
.cards-container {
  width: 100%;
  height: 70vh;
  max-height: 650px;
  min-height: 550px;
  display: flex;
  align-items: center;
  justify-content: center;
  perspective: 1800px; /* 优化透视距离，增强3D效果 */
  perspective-origin: center center;
  margin-bottom: 50px;
  overflow: visible; /* 确保所有卡片和阴影都能完整显示 */
  position: relative;
  padding: 0 200px; /* 增加左右内边距，为8张卡片的完整展开留出充足空间 */
}

.cards-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  transform-style: preserve-3d;
  transition: none; /* 移除容器过渡，让每个卡片独立动画 */
  min-width: 2800px; /* 确保有足够宽度容纳8张卡片的完整展开 */
}

/* 模块卡片 - 完美的水平展开布局优化 */
.module-card {
  position: absolute;
  width: 270px;
  height: 400px;
  background: rgba(8, 18, 28, 0.9);
  border: 2px solid rgba(0, 255, 255, 0.3);
  border-radius: 16px;
  cursor: pointer;
  transition:
    transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    opacity 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    filter 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    background 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    border-color 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    box-shadow 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  backdrop-filter: blur(20px);
  transform-style: preserve-3d;
  overflow: hidden;
  will-change: transform, opacity, filter, box-shadow;
  transform-origin: center center;

  /* 初始加载动画 - 优雅的淡入效果 */
  opacity: 0;
  transform: translateY(80px) scale(0.9);
  animation: cardFadeIn 1.0s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* 为每个卡片添加不同的延迟，从中心向外波浪式展开 */
.module-card:nth-child(1) { animation-delay: 0.7s; }
.module-card:nth-child(2) { animation-delay: 0.5s; }
.module-card:nth-child(3) { animation-delay: 0.3s; }
.module-card:nth-child(4) { animation-delay: 0.1s; } /* 中心卡片最先出现 */
.module-card:nth-child(5) { animation-delay: 0.3s; }
.module-card:nth-child(6) { animation-delay: 0.5s; }
.module-card:nth-child(7) { animation-delay: 0.7s; }
.module-card:nth-child(8) { animation-delay: 0.9s; }

@keyframes cardFadeIn {
  0% {
    opacity: 0;
    transform: translateY(80px) scale(0.9);
  }
  60% {
    opacity: 0.8;
    transform: translateY(-10px) scale(1.02);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 卡片边框发光效果 */
.card-border {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 20px;
  padding: 1px;
  background: linear-gradient(135deg,
    rgba(0, 255, 255, 0.3) 0%,
    rgba(0, 255, 255, 0.1) 25%,
    transparent 50%,
    rgba(0, 255, 255, 0.1) 75%,
    rgba(0, 255, 255, 0.3) 100%
  );
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  opacity: 0;
  transition: opacity 0.6s ease;
}

.module-card.active .card-border {
  opacity: 1;
  animation: borderGlow 2s ease-in-out infinite alternate;
}

@keyframes borderGlow {
  0% {
    background: linear-gradient(135deg,
      rgba(0, 255, 255, 0.3) 0%,
      rgba(0, 255, 255, 0.1) 25%,
      transparent 50%,
      rgba(0, 255, 255, 0.1) 75%,
      rgba(0, 255, 255, 0.3) 100%
    );
  }
  100% {
    background: linear-gradient(135deg,
      rgba(0, 255, 255, 0.6) 0%,
      rgba(0, 255, 255, 0.3) 25%,
      rgba(0, 255, 255, 0.1) 50%,
      rgba(0, 255, 255, 0.3) 75%,
      rgba(0, 255, 255, 0.6) 100%
    );
  }
}

.module-card.active {
  background: rgba(5, 15, 25, 0.95);
  border-color: rgba(0, 255, 255, 0.8);
  /* 移除强制的transform和box-shadow，让JavaScript完全控制 */
}

.module-card:not(.active) {
  background: rgba(8, 18, 28, 0.7);
  border-color: rgba(0, 255, 255, 0.2);
}

/* 悬停效果 - 适配完美的水平展开布局 */
.module-card:hover {
  border-color: rgba(0, 255, 255, 0.6) !important;
  background: rgba(10, 25, 40, 0.95) !important;
  /* 移除强制的box-shadow，让JavaScript控制阴影效果 */
}

/* 卡片反射效果 */
.card-reflection {
  position: absolute;
  bottom: -100%;
  left: 0;
  right: 0;
  height: 100%;
  background: linear-gradient(to bottom,
    rgba(0, 255, 255, 0.1) 0%,
    transparent 50%
  );
  transform: scaleY(-1);
  opacity: 0.3;
  pointer-events: none;
}

.card-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 35px 25px 30px 25px;
  text-align: center;
  position: relative;
  z-index: 2;
  box-sizing: border-box;
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 卡片图标 */
.card-icon {
  position: relative;
  flex-shrink: 0;
  margin-bottom: 10px;
}

.icon-container {
  width: 100px;
  height: 100px;
  border: 2px solid rgba(0, 255, 255, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 255, 255, 0.1);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  z-index: 2;
  transform-origin: center center;
}

.icon-container i {
  font-size: 40px;
  color: #00ffff;
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform-origin: center center;
}

.icon-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(0, 255, 255, 0.3) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: all 0.4s ease;
  animation: iconPulse 2s ease-in-out infinite;
}

@keyframes iconPulse {
  0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.3; }
  50% { transform: translate(-50%, -50%) scale(1.2); opacity: 0.6; }
}

.module-card.active .icon-container {
  border-color: rgba(0, 255, 255, 0.8);
  background: rgba(0, 255, 255, 0.2);
  box-shadow: 0 0 30px rgba(0, 255, 255, 0.5);
}

.module-card.active .icon-container i {
  color: #ffffff;
  text-shadow: 0 0 20px rgba(0, 255, 255, 1);
}

.module-card.active .icon-glow {
  opacity: 0.8;
}

/* 卡片文本区域 */
.card-text {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 20px 0;
}

/* 卡片标题 */
.card-title {
  font-size: 24px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  margin: 0 0 15px 0;
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  letter-spacing: 1px;
  line-height: 1.2;
  transform-origin: center center;
}

.card-description {
  font-size: 15px;
  color: rgba(255, 255, 255, 0.6);
  margin: 0;
  line-height: 1.6;
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  max-width: 100%;
  transform-origin: center center;
}

.module-card.active .card-title {
  color: #00ffff;
  text-shadow: 0 0 20px rgba(0, 255, 255, 0.8);
}

.module-card.active .card-description {
  color: rgba(255, 255, 255, 0.8);
}

/* 进入按钮 */
.card-button {
  width: 100%;
  flex-shrink: 0;
  padding: 0;
  margin: 0;
}

.enter-btn {
  width: 100%;
  background: rgba(0, 255, 255, 0.15);
  border: 2px solid rgba(0, 255, 255, 0.4);
  color: #00ffff;
  padding: 18px 24px;
  border-radius: 35px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  backdrop-filter: blur(15px);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  text-transform: uppercase;
  letter-spacing: 1.5px;
  box-shadow: 0 4px 15px rgba(0, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
  transform-origin: center center;
  opacity: 0;
  transform: translateY(20px) scale(0.9);
  animation: buttonSlideIn 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.3s forwards;
}

@keyframes buttonSlideIn {
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.enter-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.6s ease;
}

.enter-btn:hover {
  background: rgba(0, 255, 255, 0.25);
  border-color: rgba(0, 255, 255, 0.7);
  box-shadow:
    0 6px 25px rgba(0, 255, 255, 0.4),
    0 0 40px rgba(0, 255, 255, 0.3);
  transform: translateY(-3px);
}

.enter-btn:hover::before {
  left: 100%;
}

.enter-btn i {
  font-size: 14px;
  transition: transform 0.3s ease;
}

.enter-btn:hover i {
  transform: translateX(4px);
}

/* 底部指示器 */
.bottom-indicators {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
  flex-wrap: nowrap; /* 强制不换行 */
  width: 100%;
  margin-top: 60px;
  padding: 20px 10px;
  overflow-x: auto; /* 如果内容过宽，允许水平滚动 */
  overflow-y: hidden;
}

/* 隐藏滚动条但保持滚动功能 */
.bottom-indicators::-webkit-scrollbar {
  display: none;
}

.bottom-indicators {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.indicator {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  transition: all 0.4s ease;
  padding: 10px 16px;
  border-radius: 25px;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(0, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  min-width: 120px;
  max-width: 140px;
  justify-content: center;
  flex-shrink: 0; /* 防止收缩 */
}

.indicator:hover {
  background: rgba(0, 255, 255, 0.1);
  border-color: rgba(0, 255, 255, 0.4);
  transform: translateY(-2px);
}

.indicator-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(0, 255, 255, 0.4);
  transition: all 0.4s ease;
  position: relative;
  flex-shrink: 0;
}

.indicator-dot::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: rgba(0, 255, 255, 0.1);
  transform: translate(-50%, -50%);
  transition: all 0.4s ease;
  opacity: 0;
}

.indicator-label {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.7);
  transition: all 0.4s ease;
  text-align: center;
  letter-spacing: 0.5px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.indicator.active {
  background: rgba(0, 255, 255, 0.15);
  border-color: rgba(0, 255, 255, 0.6);
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
}

.indicator.active .indicator-dot {
  background: #00ffff;
  box-shadow: 0 0 15px rgba(0, 255, 255, 0.8);
  transform: scale(1.2);
}

.indicator.active .indicator-dot::before {
  opacity: 1;
  background: rgba(0, 255, 255, 0.3);
  width: 24px;
  height: 24px;
  animation: dotPulse 2s ease-in-out infinite;
}

.indicator.active .indicator-label {
  color: #00ffff;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

@keyframes dotPulse {
  0%, 100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.3;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0.6;
  }
}

/* 响应式设计 - 优化所有屏幕尺寸的卡片展示 */
@media (max-width: 1400px) {
  .cards-container {
    padding: 0 120px; /* 减少内边距适应中等屏幕 */
  }

  .cards-wrapper {
    min-width: 2400px; /* 适当减少容器宽度 */
  }
}

@media (max-width: 1200px) {
  .main-title {
    font-size: 3rem;
  }

  .cards-container {
    height: 65vh;
    max-height: 580px;
    min-height: 500px;
    padding: 0 100px; /* 进一步减少内边距 */
  }

  .cards-wrapper {
    min-width: 2200px;
  }

  .module-card {
    width: 250px;
    height: 380px;
  }

  .card-content {
    padding: 25px 18px 20px 18px;
  }

  .bottom-indicators {
    gap: 12px;
    padding: 20px 5px;
  }

  .indicator {
    min-width: 110px;
    max-width: 120px;
    padding: 8px 14px;
  }

  .indicator-label {
    font-size: 12px;
  }
}

@media (max-width: 768px) {
  .main-title {
    font-size: 2.5rem;
    letter-spacing: 2px;
  }

  .cards-container {
    height: 55vh;
    max-height: 480px;
    min-height: 400px;
    margin-bottom: 40px;
    padding: 0 60px; /* 进一步减少内边距，确保卡片可见 */
  }

  .cards-wrapper {
    min-width: 1800px; /* 确保有足够空间展示所有卡片 */
  }

  .module-card {
    width: 200px;
    height: 320px;
  }

  .card-content {
    padding: 25px 18px 22px 18px;
  }

  .card-text {
    padding: 15px 0;
  }

  .icon-container {
    width: 80px;
    height: 80px;
  }

  .icon-container i {
    font-size: 32px;
  }

  .card-title {
    font-size: 20px;
    margin: 0 0 10px 0;
  }

  .card-description {
    font-size: 13px;
  }

  .enter-btn {
    padding: 16px 20px;
    font-size: 15px;
  }

  .bottom-indicators {
    gap: 10px;
    margin-top: 40px;
    padding: 15px 5px;
  }

  .indicator {
    min-width: 100px;
    max-width: 110px;
    padding: 8px 12px;
  }

  .indicator-label {
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .main-title {
    font-size: 2rem;
    letter-spacing: 1px;
  }

  .header-section {
    margin-bottom: 25px;
  }

  .cards-container {
    height: 50vh;
    max-height: 450px;
    min-height: 380px;
    margin-bottom: 35px;
  }

  .module-card {
    width: 240px;
    height: 360px;
  }

  .card-content {
    padding: 20px 15px 18px 15px;
  }

  .card-text {
    padding: 12px 0;
  }

  .icon-container {
    width: 70px;
    height: 70px;
  }

  .icon-container i {
    font-size: 28px;
  }

  .card-title {
    font-size: 18px;
    margin: 0 0 8px 0;
  }

  .card-description {
    font-size: 12px;
  }

  .enter-btn {
    padding: 14px 18px;
    font-size: 14px;
  }

  .bottom-indicators {
    gap: 8px;
    margin-top: 30px;
    padding: 15px 5px;
  }

  .indicator {
    min-width: 90px;
    max-width: 100px;
    padding: 6px 10px;
  }

  .indicator-dot {
    width: 10px;
    height: 10px;
  }

  .indicator-label {
    font-size: 10px;
  }

  .logout-container {
    top: 20px;
    right: 20px;
  }

  .logout-btn {
    padding: 10px 16px;
    font-size: 12px;
  }
}


</style>
